import chromium from "@sparticuz/chromium";
import puppeteer from "puppeteer-core";

export const lambdaHandler = async (event, context) => {
  let browser = null;
  let page = null;

  // --- VALIDATE REQUEST BODY ---
  const { url } = JSON.parse(event.body) ?? {};

  if (!url) {
    return {
      statusCode: 400,
      body: JSON.stringify({ error: "Invalid request body!" }),
    };
  }

  try {
    // --- LAUNCH BROWSER ---
    browser = await puppeteer.launch({
      args: chromium.args,
      defaultViewport: chromium.defaultViewport,
      executablePath: await chromium.executablePath(),
      headless: chromium.headless,
      ignoreHTTPSErrors: true,
    });

    // --- CREATE PAGE ---
    page = await browser.newPage();

    // --- GO TO URL ---
    await page.goto(url);

    // --- GET TITLE ---
    const title = await page.title();

    // --- R<PERSON><PERSON>N TITLE ---
    return {
      statusCode: 200,
      body: JSON.stringify({
        message: "Fetched title successfully",
        title: title,
      }),
    };
  } catch (error) {
    // --- CATCH ERRORS ---
    console.error(error);
    return {
      statusCode: 500,
      body: JSON.stringify({
        message: "Error fetching title",
        error: error.message,
      }),
    };
  } finally {
    // --- CLOSE PAGE ---
    if (page) {
      await page.close();
    }

    // --- CLOSE BROWSER ---
    if (browser) {
      await browser.close();
    }
  }
};
