import chromium from "@sparticuz/chromium";
import puppeteer from "puppeteer-core";

export const lambdaHandler = async (event, context) => {
  let browser = null;
  let page = null;

  // --- VALIDATE REQUEST BODY ---
  const { content } = JSON.parse(event.body) ?? {};

  if (!content) {
    return {
      statusCode: 400,
      body: JSON.stringify({ error: "Expecting content!" }),
    };
  }

  try {
    // --- LAUNCH BROWSER ---
    browser = await puppeteer.launch({
      args: chromium.args,
      defaultViewport: chromium.defaultViewport,
      executablePath: await chromium.executablePath(),
      headless: chromium.headless,
      ignoreHTTPSErrors: true,
    });

    // --- CREATE PAGE ---
    page = await browser.newPage();

    // Set viewport for better interaction
    await page.setViewport({ width: 1920, height: 1024 });

    // --- NAVIGATE TO TEST-COMMITS PAGE ---
    console.log("Navigating to test-commits page...");
    await page.goto("http://localhost:3001/test-commits", {
      waitUntil: "networkidle2",
      timeout: 30000,
    });

    // --- WAIT FOR EDITOR TO LOAD ---
    console.log("Waiting for TipTap editor to load...");
    await page.waitForSelector(".ProseMirror", { timeout: 15000 });

    // Wait a bit more for the editor to be fully interactive
    await page.waitForTimeout(2000);

    // --- ADD CONTENT FROM EVENT TO EDITOR ---
    console.log("Adding content from event to editor...");

    // Click on the editor to focus it
    await page.click(".ProseMirror");
    await page.waitForTimeout(500);

    // Type the content from the event
    await page.type(".ProseMirror", content);
    await page.waitForTimeout(1000);

    // --- TRIGGER FIRST SAVE ---
    console.log("Triggering first save...");
    await page.keyboard.down("Control");
    await page.keyboard.press("s");
    await page.keyboard.up("Control");
    await page.waitForTimeout(2000);

    // --- ADD MORE CONTENT ---
    console.log("Adding more content...");
    await page.keyboard.press("Enter");
    await page.type(
      ".ProseMirror",
      "\nThis is additional content added after the first save."
    );
    await page.waitForTimeout(1000);

    // --- TRIGGER SECOND SAVE ---
    console.log("Triggering second save...");
    await page.keyboard.down("Control");
    await page.keyboard.press("s");
    await page.keyboard.up("Control");
    await page.waitForTimeout(2000);

    // --- MAKE EDITS TO EXISTING CONTENT ---
    console.log("Making edits to existing content...");
    // Select some text and replace it
    await page.keyboard.down("Control");
    await page.keyboard.press("a");
    await page.keyboard.up("Control");
    await page.waitForTimeout(500);

    await page.keyboard.press("ArrowRight");
    await page.type(".ProseMirror", " EDITED");
    await page.waitForTimeout(1000);

    // --- TRIGGER THIRD SAVE ---
    console.log("Triggering third save...");
    await page.keyboard.down("Control");
    await page.keyboard.press("s");
    await page.keyboard.up("Control");
    await page.waitForTimeout(2000);

    // Find and click debug button
    console.log("🔍 Looking for debug panel...");

    // Try multiple selector strategies for the debug button
    let debugButton = null;
    try {
      // First try the text-based selector
      await page.waitForSelector('button:has-text("Generate Debug Data")', {
        timeout: 5000,
      });
      debugButton = await page.$('button:has-text("Generate Debug Data")');
    } catch (error) {
      console.log("Text selector failed, trying alternative selectors...");

      // Try finding button by text content
      debugButton = await page.evaluateHandle(() => {
        const buttons = Array.from(document.querySelectorAll("button"));
        return buttons.find(
          (button) =>
            button.textContent &&
            button.textContent.includes("Generate Debug Data")
        );
      });

      if (!debugButton || debugButton.asElement() === null) {
        throw new Error("Could not find Generate Debug Data button");
      }
    }

    console.log("🖱️ Scrolling to and clicking Generate Debug Data button...");

    // Scroll the button into view
    await page.evaluate((button) => {
      if (button) {
        button.scrollIntoView({ behavior: "smooth", block: "center" });
      }
    }, debugButton);

    // Click the button
    await debugButton.click();

    // Extract JSON data
    console.log("📊 Extracting JSON payload...");
    await page.waitForSelector("textarea[readonly]", { timeout: 10000 });

    const jsonPayload = await page.evaluate(() => {
      const textarea = document.querySelector("textarea[readonly]");
      if (!textarea || !textarea.value) {
        throw new Error("No JSON data found in debug panel");
      }

      try {
        return JSON.parse(textarea.value);
      } catch (error) {
        throw new Error("Failed to parse JSON data: " + error.message);
      }
    });

    // Extract and validate trackState
    console.log("🎯 Extracting trackState data...");
    const trackStateData = jsonPayload.data;

    if (!trackStateData) {
      throw new Error("No trackState data found in JSON payload");
    }

    const { blameMap, commits } = trackStateData;

    if (!blameMap || !commits) {
      throw new Error(
        "Invalid trackState structure - missing blameMap or commits"
      );
    }

    console.log("✅ Success! TrackState extracted successfully:");
    console.log(`   📝 Document ID: ${jsonPayload.id}`);
    console.log(`   📊 Commits: ${commits.length}`);
    console.log(`   🗺️ BlameMap entries: ${blameMap.length}`);
    console.log(
      `   📄 Content length: ${
        jsonPayload.content ? jsonPayload.content.length : 0
      } characters`
    );

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: "Successfully extracted trackState data",
        trackState: trackStateData,
        summary: {
          commitsCount: commits.length,
          blameMapEntries: blameMap.length,
          documentId: jsonPayload.id,
          contentLength: jsonPayload.content ? jsonPayload.content.length : 0,
        },
      }),
    };
  } catch (error) {
    // --- CATCH ERRORS ---
    console.error("Error in puppeteer automation:", error);

    // Try to capture screenshot for debugging
    let screenshot = null;
    try {
      if (page) {
        screenshot = await page.screenshot({ encoding: "base64" });
      }
    } catch (screenshotError) {
      console.error("Failed to capture screenshot:", screenshotError);
    }

    return {
      statusCode: 500,
      body: JSON.stringify({
        message: "Error in test-commits automation",
        error: error.message,
        screenshot: screenshot,
      }),
    };
  } finally {
    // --- CLOSE PAGE ---
    if (page) {
      await page.close();
    }

    // --- CLOSE BROWSER ---
    if (browser) {
      await browser.close();
    }
  }
};
