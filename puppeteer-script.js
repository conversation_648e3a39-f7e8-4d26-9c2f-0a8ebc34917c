import chromium from "@sparticuz/chromium";
import puppeteer from "puppeteer-core";

export const lambdaHandler = async (event, context) => {
  let browser = null;
  let page = null;

  // --- VALIDATE REQUEST BODY ---
  const { content } = JSON.parse(event.body) ?? {};

  if (!content) {
    return {
      statusCode: 400,
      body: JSON.stringify({ error: "Expecting content!" }),
    };
  }

  try {
    // --- LAUNCH BROWSER ---
    browser = await puppeteer.launch({
      args: chromium.args,
      defaultViewport: chromium.defaultViewport,
      executablePath: await chromium.executablePath(),
      headless: chromium.headless,
      ignoreHTTPSErrors: true,
    });

    // --- CREATE PAGE ---
    page = await browser.newPage();

    // Set viewport for better interaction
    await page.setViewport({ width: 1280, height: 720 });

    // --- NAVIGATE TO TEST-COMMITS PAGE ---
    console.log("Navigating to test-commits page...");
    await page.goto("http://localhost:3001/test-commits", {
      waitUntil: "networkidle2",
      timeout: 30000,
    });

    // --- WAIT FOR EDITOR TO LOAD ---
    console.log("Waiting for TipTap editor to load...");
    await page.waitForSelector(".ProseMirror", { timeout: 15000 });

    // Wait a bit more for the editor to be fully interactive
    await page.waitForTimeout(2000);

    // --- ADD SAMPLE CONTENT TO EDITOR ---
    console.log("Adding sample content to editor...");

    // Click on the editor to focus it
    await page.click(".ProseMirror");
    await page.waitForTimeout(500);

    // Type initial content
    await page.type(
      ".ProseMirror",
      "This is the initial content for testing change tracking."
    );
    await page.waitForTimeout(1000);

    // --- TRIGGER FIRST SAVE ---
    console.log("Triggering first save...");
    await page.keyboard.down("Control");
    await page.keyboard.press("s");
    await page.keyboard.up("Control");
    await page.waitForTimeout(2000);

    // --- ADD MORE CONTENT ---
    console.log("Adding more content...");
    await page.keyboard.press("Enter");
    await page.type(
      ".ProseMirror",
      "\nThis is additional content added after the first save."
    );
    await page.waitForTimeout(1000);

    // --- TRIGGER SECOND SAVE ---
    console.log("Triggering second save...");
    await page.keyboard.down("Control");
    await page.keyboard.press("s");
    await page.keyboard.up("Control");
    await page.waitForTimeout(2000);

    // --- MAKE EDITS TO EXISTING CONTENT ---
    console.log("Making edits to existing content...");
    // Select some text and replace it
    await page.keyboard.down("Control");
    await page.keyboard.press("a");
    await page.keyboard.up("Control");
    await page.waitForTimeout(500);

    await page.keyboard.press("ArrowRight");
    await page.type(".ProseMirror", " EDITED");
    await page.waitForTimeout(1000);

    // --- TRIGGER THIRD SAVE ---
    console.log("Triggering third save...");
    await page.keyboard.down("Control");
    await page.keyboard.press("s");
    await page.keyboard.up("Control");
    await page.waitForTimeout(2000);

    // --- WAIT FOR DEBUG PANEL TO BE AVAILABLE ---
    console.log("Looking for debug panel...");
    await page.waitForSelector('button:has-text("Generate Debug Data")', {
      timeout: 10000,
    });

    // --- CLICK GENERATE DEBUG DATA BUTTON ---
    console.log("Clicking Generate Debug Data button...");
    await page.click('button:has-text("Generate Debug Data")');
    await page.waitForTimeout(3000);

    // --- WAIT FOR JSON DATA TO BE GENERATED ---
    console.log("Waiting for JSON data to be generated...");
    await page.waitForSelector("textarea[readonly]", { timeout: 10000 });

    // --- EXTRACT THE JSON PAYLOAD ---
    console.log("Extracting JSON payload...");
    const jsonPayload = await page.evaluate(() => {
      const textarea = document.querySelector("textarea[readonly]");
      if (!textarea || !textarea.value) {
        throw new Error("No JSON data found in debug panel");
      }

      try {
        return JSON.parse(textarea.value);
      } catch (error) {
        throw new Error("Failed to parse JSON data: " + error.message);
      }
    });

    // --- EXTRACT TRACKSTATE DATA ---
    console.log("Extracting trackState data...");
    const trackStateData = jsonPayload.data;

    if (!trackStateData) {
      throw new Error("No trackState data found in JSON payload");
    }

    // --- VALIDATE TRACKSTATE STRUCTURE ---
    const { blameMap, commits } = trackStateData;

    if (!blameMap || !commits) {
      throw new Error(
        "Invalid trackState structure - missing blameMap or commits"
      );
    }

    console.log(
      `Successfully extracted trackState with ${commits.length} commits and ${blameMap.length} blame map entries`
    );

    // --- RETURN TRACKSTATE DATA ---
    return {
      statusCode: 200,
      body: JSON.stringify({
        message: "Successfully extracted trackState data",
        trackState: trackStateData,
        summary: {
          commitsCount: commits.length,
          blameMapEntries: blameMap.length,
          documentId: jsonPayload.id,
          contentLength: jsonPayload.content ? jsonPayload.content.length : 0,
        },
      }),
    };
  } catch (error) {
    // --- CATCH ERRORS ---
    console.error("Error in puppeteer automation:", error);

    // Try to capture screenshot for debugging
    let screenshot = null;
    try {
      if (page) {
        screenshot = await page.screenshot({ encoding: "base64" });
      }
    } catch (screenshotError) {
      console.error("Failed to capture screenshot:", screenshotError);
    }

    return {
      statusCode: 500,
      body: JSON.stringify({
        message: "Error in test-commits automation",
        error: error.message,
        screenshot: screenshot,
      }),
    };
  } finally {
    // --- CLOSE PAGE ---
    if (page) {
      await page.close();
    }

    // --- CLOSE BROWSER ---
    if (browser) {
      await browser.close();
    }
  }
};
