import React from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/common/Card";
import {
  ChevronRight,
  FileText,
  Check,
  Edit,
  ExternalLink,
  FileSignature,
  PenTool,
  Timer,
} from "lucide-react";
import Button from "@/components/common/Button";
import { Link } from "react-router-dom";
import AnimatedTransition from "@/components/common/AnimatedTransition";
import { useCompanyDetails } from "@/components/questions/hooks/useCompanyDetails";
import GettingStartedSectionToggleGroup from "./GettingStartedSectionToggleGroup";
import { APIClient } from "@/integrations/legal-concierge/client";
import { useAuth } from "@/contexts/AuthContext";
import { useUserPermissions } from "@/integrations/legal-concierge/hooks/useUserProfile";
import { RoleBasedAccess } from "@/components/auth/RoleBasedAccess";
import { useCompanySelection } from "@/contexts/CompanySelectionContext";

const GettingStartedCard: React.FC = () => {
  const { companyDetails, loading: companyDetailsLoading } =
    useCompanyDetails();
  const { loading: authLoading, isAuthenticated, user } = useAuth();
  const { isCompanySelected } = useCompanySelection();
  const permissions = useUserPermissions(user, isCompanySelected);
  const [mode, setMode] = React.useState<
    "getting-started" | "register-existing"
  >("getting-started");

  React.useEffect(() => {
    const checkAuthAndMode = async () => {
      setMode(
        companyDetails?.registerMode == "register-existing"
          ? "register-existing"
          : "getting-started"
      );
    };

    checkAuthAndMode();
  }, [companyDetails?.registerMode]);

  // Wait for both auth and company details to load before determining completion status
  const isLoading = authLoading || companyDetailsLoading;

  // Only proceed with status checks if we have valid company details
  if (isLoading || !companyDetails) {
    return (
      <AnimatedTransition delay={0.5} className="mb-8">
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Loading...</CardTitle>
            <CardDescription>
              Please wait while we load your company information
            </CardDescription>
          </CardHeader>
        </Card>
      </AnimatedTransition>
    );
  }

  const detailsCompleted = companyDetails.isFormConfirmed;
  const documentsReviewed = companyDetails.isReviewConfirmed;
  const documentsSignatureConfirmed = companyDetails.isSignatureConfirmed;
  const documentsSignatureComplete = companyDetails.isSignatureComplete;
  const einApplicationCompleted = companyDetails.isEinApplyComplete;
  const businessAccountSetupCompleted =
    companyDetails.isOpeningBankAccountComplete;
  const foreignQualificationCompleted =
    companyDetails.isForeignQualificationToDoBusinessComplete;
  const postIncorporationCompleted =
    companyDetails.isPostIncorporationConfirmed;
  const questionnaireProgress = companyDetails.isFormConfirmed ? "100" : "0";
  if (!isLoading) {
    console.log("detailsCompleted", detailsCompleted);
    console.log("documentsReviewed", documentsReviewed);
    console.log("documentsSignatureConfirmed", documentsSignatureConfirmed);
    console.log("documentsSignatureComplete", documentsSignatureComplete);
    console.log("postIncorporationCompleted", postIncorporationCompleted);
    console.log("questionnaireProgress", questionnaireProgress);
  }

  // Persist mode toggle in localStorage for persistence
  const handleModeChange = async (
    value: "getting-started" | "register-existing"
  ) => {
    setMode(value);
    const api = new APIClient();
    await api.updateCompanyRegistrationMode(
      companyDetails.id,
      value === "register-existing" ? 1 : 0
    );
    localStorage.setItem(
      "registerMode",
      value === "register-existing" ? "true" : "false"
    );
  };

  return (
    <AnimatedTransition delay={0.5} className="mb-8">
      <Card className="mb-8">
        <CardHeader className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
          {mode === "getting-started" && (
            <div>
              <CardTitle>Getting Started</CardTitle>
              <CardDescription>
                Follow these steps to form your company
              </CardDescription>
            </div>
          )}
          {mode === "register-existing" && (
            <div>
              <CardTitle>Register Existing</CardTitle>
              <CardDescription>
                Follow these steps to register your already incorporated company
              </CardDescription>
            </div>
          )}

          <GettingStartedSectionToggleGroup
            value={mode}
            onChange={handleModeChange}
            options={[
              { label: "Getting Started", value: "getting-started" },
              { label: "Register Existing", value: "register-existing" },
            ]}
            formData={companyDetails}
          />
        </CardHeader>

        <CardContent>
          <div className="space-y-4">
            {/* "Already incorporated?" Box only in Getting Started mode */}
            {mode === "getting-started" && !companyDetails.isFormConfirmed && (
              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-2 p-4 bg-blue-50 text-blue-800 rounded-lg">
                <div>
                  <h3 className="font-medium">
                    Already incorporated your company?
                  </h3>
                  <p className="text-sm mt-1">
                    If you've already formed your company elsewhere, you can
                    register it here.
                  </p>
                </div>
                <Button
                  size="sm"
                  variant="primary"
                  className="flex-shrink-0 whitespace-nowrap w-full sm:w-auto"
                  disabled={!isAuthenticated}
                  onClick={() => handleModeChange("register-existing")}
                >
                  Register Existing <ExternalLink className="ml-1 h-4 w-4" />
                </Button>
              </div>
            )}

            {/* Common Section - Enter company details - Available for both OWNER and COLLABORATOR */}
            <SectionItem
              step="1"
              title={
                permissions.canEditForms
                  ? "Enter company details"
                  : "View company details"
              }
              description={
                permissions.canEditForms
                  ? "Provide information about your company name, address, and structure."
                  : "View the company information and structure (read-only access)."
              }
              completed={questionnaireProgress === "100" && detailsCompleted}
              link={
                permissions.canEditForms
                  ? detailsCompleted
                    ? "/questions?edit=true"
                    : "/questions"
                  : "/questions"
              }
              disabled={
                permissions.canEditForms ? documentsSignatureConfirmed : false
              }
            />

            {/* Mode-specific document sections */}
            {mode === "getting-started" ? (
              <>
                {/* Review Documents Section */}
                <SectionItem
                  step="2"
                  title="Review documents"
                  description="Review the generated legal documents and confirm your review."
                  completed={documentsReviewed}
                  link="/review-documents"
                  disabled={!(detailsCompleted && !documentsSignatureConfirmed)}
                  icon={<FileText size={16} />}
                />

                {/* Sign Documents Section */}
                <SectionItem2
                  step="3"
                  title="Sign documents"
                  description={
                    documentsSignatureConfirmed && !documentsSignatureComplete
                      ? "Documents are sent for signature. Waiting for signers..."
                      : "Sign the reviewed documents electronically."
                  }
                  completed={documentsSignatureComplete}
                  link="/data-room"
                  disabled={true}
                />

                {/* Post Incorporation Section - Only for OWNER role */}
                <RoleBasedAccess requiredPermission="canAccessPostIncorporation">
                  <SectionItem
                    step="4"
                    title="Complete post-incorporation tasks"
                    description="Get your EIN, set up a business bank account, and more."
                    completed={postIncorporationCompleted}
                    link="/post-incorporation"
                    disabled={
                      !documentsSignatureConfirmed || postIncorporationCompleted
                    }
                  />
                </RoleBasedAccess>
              </>
            ) : (
              <>
                {/* Upload Documents Section for register-existing mode */}
                <SectionItem
                  step="2"
                  title="Upload documents"
                  description="Upload your company's formation documents for our review."
                  completed={documentsReviewed}
                  link="/data-room"
                  disabled={!detailsCompleted}
                  icon={<FileText size={16} />}
                />

                {/* Post Incorporation Section - Only for OWNER role */}
                <RoleBasedAccess requiredPermission="canAccessPostIncorporation">
                  <SectionItem
                    step="3"
                    title="Complete post-incorporation tasks"
                    description="Get your EIN, set up a business bank account, and more."
                    completed={postIncorporationCompleted}
                    link="/post-incorporation"
                    disabled={!documentsReviewed || postIncorporationCompleted}
                  />
                </RoleBasedAccess>
              </>
            )}
          </div>
        </CardContent>
      </Card>
    </AnimatedTransition>
  );
};

// Reusable component for sections
interface SectionItemProps {
  step: string;
  title: string;
  description: string;
  completed: boolean;
  link: string;
  disabled?: boolean;
  icon?: React.ReactNode;
}

const SectionItem: React.FC<SectionItemProps> = ({
  step,
  title,
  description,
  completed,
  link,
  disabled,
  icon,
}) => {
  console.log(step, title, completed, disabled);
  const buttonIcon =
    icon ?? (completed ? <Edit size={16} /> : <ChevronRight size={16} />);
  const buttonText = completed ? "View" : "Start";

  return (
    <div
      className={`flex items-start p-4 rounded-lg ${!disabled ? "hover:bg-gray-50" : "opacity-50"} transition-colors`}
    >
      <div
        className={`flex-shrink-0 rounded-full h-10 w-10 flex items-center justify-center mr-4 ${completed ? "bg-green-100" : "bg-legal-100"}`}
      >
        {completed ? (
          <Check className="h-5 w-5 text-green-600" />
        ) : (
          <span className="text-legal-600 font-medium">{step}</span>
        )}
      </div>
      <div className="flex-1">
        <h3 className="font-medium text-gray-900">{title}</h3>
        <p className="text-gray-600 text-sm mt-1">{description}</p>
        {completed && <p className="text-green-600 text-sm mt-1">Completed</p>}
      </div>
      {disabled ? (
        <Button
          size="sm"
          variant={completed ? "outline" : "primary"}
          className="flex-shrink-0 mt-1 cursor-not-allowed"
          disabled={true}
        >
          <span className="flex items-center">
            {buttonText}
            <span className="ml-2">{buttonIcon}</span>
          </span>
        </Button>
      ) : (
        <Button
          size="sm"
          variant={completed ? "outline" : "primary"}
          className="flex-shrink-0 mt-1"
          asChild
        >
          <Link to={link} className="flex items-center">
            {buttonText}
            <span className="ml-2">{buttonIcon}</span>
          </Link>
        </Button>
      )}
    </div>
  );
};

const SectionItem2: React.FC<SectionItemProps> = ({
  step,
  title,
  description,
  completed,
  link,
  disabled,
  icon,
}) => {
  console.log(step, title, completed, disabled);
  const buttonIcon =
    icon ?? (completed ? <PenTool size={16} /> : <Timer size={16} />);
  const buttonText = completed ? "View" : "Waiting...";

  return (
    <div
      className={`flex items-start p-4 rounded-lg ${!disabled ? "hover:bg-gray-50" : "opacity-50"} transition-colors`}
    >
      <div
        className={`flex-shrink-0 rounded-full h-10 w-10 flex items-center justify-center mr-4 ${completed ? "bg-green-100" : "bg-legal-100"}`}
      >
        {completed ? (
          <Check className="h-5 w-5 text-green-600" />
        ) : (
          <span className="text-legal-600 font-medium">{step}</span>
        )}
      </div>
      <div className="flex-1">
        <h3 className="font-medium text-gray-900">{title}</h3>
        <p className="text-gray-600 text-sm mt-1">{description}</p>
        {completed && <p className="text-green-600 text-sm mt-1">Completed</p>}
      </div>
      {disabled ? (
        <Button
          size="sm"
          variant={completed ? "outline" : "primary"}
          className="flex-shrink-0 mt-1 cursor-not-allowed"
          disabled={true}
        >
          <span className="flex items-center">
            {buttonText}
            <span className="ml-2">{buttonIcon}</span>
          </span>
        </Button>
      ) : (
        <Button
          size="sm"
          variant={completed ? "outline" : "primary"}
          className="flex-shrink-0 mt-1"
          asChild
        >
          <Link to={link} className="flex items-center">
            {buttonText}
            <span className="ml-2">{buttonIcon}</span>
          </Link>
        </Button>
      )}
    </div>
  );
};

export default GettingStartedCard;
