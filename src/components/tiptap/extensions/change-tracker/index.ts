import { Plugin, PluginKey } from "@tiptap/pm/state";
import { DecorationSet } from "@tiptap/pm/view";
import { Extension, Mark, mergeAttributes } from "@tiptap/core";
import { EditorState } from "@tiptap/pm/state";
import {
  Span,
  Commit,
  TrackState,
  serializeTrackStateToAPI,
  createTrackStateFromAPI,
  updateBlameMap,
  calculateCommitDiff,
  convertHTMLToJSON,
} from "./utils";

// Constants
const MARK_INSERTION = "insertion";
const MARK_DELETION = "deletion";
const EXTENSION_NAME = "trackchange";

// Plugin key for the track changes plugin
const trackPluginKey = new PluginKey("trackChanges");

// Main tracking plugin with proper batching
const trackPlugin = new Plugin({
  key: trackPluginKey,
  state: {
    init(_, instance) {
      return new TrackState(
        [new Span(0, instance.doc.content.size, null)],
        [],
        [],
        [],
        []
      );
    },
    apply(tr, tracked) {
      // Check if this is a restoration transaction - if so, restore the saved state instead of tracking
      const isRestoration = tr.getMeta("isRestoration");
      if (isRestoration) {
        const restoredTrackState = tr.getMeta("restoredTrackState");
        if (restoredTrackState) {
          // Restore the saved track state
          return restoredTrackState;
        }
        // If no saved state, just return current without tracking changes
        return tracked;
      }

      // Check if this is a checkout transaction - don't track document changes from checkout
      const isCheckout = tr.getMeta("isCheckout");
      if (isCheckout) {
        return tracked; // Don't track checkout changes
      }

      if (tr.docChanged) tracked = tracked.applyTransform(tr);
      const commitMessage = tr.getMeta(trackPluginKey);
      if (commitMessage)
        tracked = tracked.applyCommit(commitMessage, new Date(tr.time));
      return tracked;
    },
  },
});

// Plugin for highlighting changes
const highlightPluginKey = new PluginKey("highlightChanges");

const highlightPlugin = new Plugin({
  key: highlightPluginKey,
  state: {
    init() {
      return { deco: DecorationSet.empty, commit: null };
    },
    apply(tr, prev, oldState, state) {
      const highlight = tr.getMeta(highlightPluginKey);
      if (highlight && highlight.add != null && prev.commit != highlight.add) {
        const tState = trackPlugin.getState(state);
        const decos = calculateCommitDiff(tState, highlight.add);
        return {
          deco: DecorationSet.create(state.doc, decos),
          commit: highlight.add,
        };
      } else if (
        highlight &&
        (highlight.clear === true || highlight.clear != null)
      ) {
        return { deco: DecorationSet.empty, commit: null };
      } else if (tr.docChanged && prev.commit) {
        return { deco: prev.deco.map(tr.mapping, tr.doc), commit: prev.commit };
      } else {
        return prev;
      }
    },
  },
  props: {
    decorations(state) {
      return this.getState(state).deco;
    },
  },
});

// Insertion mark for tracking additions
export const InsertionMark = Mark.create({
  name: MARK_INSERTION,
  addAttributes() {
    return {
      "data-user-id": {
        type: "string",
        default: "",
      },
      "data-user-name": {
        type: "string",
        default: "",
      },
      "data-timestamp": {
        type: "string",
        default: "",
      },
    };
  },
  parseHTML() {
    return [{ tag: "ins" }];
  },
  renderHTML({ HTMLAttributes }) {
    return [
      "ins",
      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, {
        style: "background-color: #d4edda; text-decoration: none;",
      }),
      0,
    ];
  },
});

// Deletion mark for tracking deletions
export const DeletionMark = Mark.create({
  name: MARK_DELETION,
  addAttributes() {
    return {
      "data-user-id": {
        type: "string",
        default: "",
      },
      "data-user-name": {
        type: "string",
        default: "",
      },
      "data-timestamp": {
        type: "string",
        default: "",
      },
    };
  },
  parseHTML() {
    return [{ tag: "del" }];
  },
  renderHTML({ HTMLAttributes }) {
    return [
      "del",
      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, {
        style: "background-color: #f8d7da; text-decoration: line-through;",
      }),
      0,
    ];
  },
});

declare module "@tiptap/core" {
  interface Commands<ReturnType> {
    trackchange: {
      /**
       * Commit current changes with a message
       */
      commitChanges: (message: string) => ReturnType;
      /**
       * Check if there are uncommitted changes
       */
      hasUncommittedChanges: () => ReturnType;
      /**
       * Get the current track state
       */
      getTrackState: () => ReturnType;
      /**
       * Highlight a specific commit
       */
      highlightCommit: (commit: Commit | null) => ReturnType;
      /**
       * Clear all highlights
       */
      clearAllHighlights: () => ReturnType;
      /**
       * Restore latest content without highlights
       */
      restoreLatestContent: () => ReturnType;
      /**
       * Save document (commits changes)
       */
      saveDocument: (message?: string) => ReturnType;
      /**
       * Prepare document for backend save and return payload
       */
      saveToBackend: (documentId?: string, title?: string) => any;
      /**
       * Load document from backend data
       */
      loadFromBackend: (documentData: any) => ReturnType;
      /**
       * Checkout to a specific commit
       */
      checkoutCommit: (targetCommit: Commit, force?: boolean) => ReturnType;
      /**
       * Return to the latest state from checkout mode
       */
      returnToLatest: () => ReturnType;
      /**
       * Set the document ID for this editor instance
       */
      setDocumentId: (documentId: string) => ReturnType;
    };
  }
}

// Main extension
export const TrackChangeExtension = Extension.create<{
  enabled: boolean;
  userId?: string;
  userName?: string;
}>({
  name: EXTENSION_NAME,

  addOptions() {
    return {
      enabled: true,
      userId: "",
      userName: "",
    };
  },

  addExtensions() {
    return [InsertionMark, DeletionMark];
  },

  addProseMirrorPlugins() {
    return [trackPlugin, highlightPlugin];
  },

  addCommands() {
    return {
      commitChanges:
        (message: string) =>
        ({ tr, dispatch }) => {
          if (dispatch) {
            tr.setMeta(trackPluginKey, message);
            dispatch(tr);
          }
          return true;
        },

      hasUncommittedChanges:
        () =>
        ({ state }) => {
          const trackState = trackPlugin.getState(state);
          return trackState?.uncommittedSteps.length > 0;
        },

      getTrackState:
        () =>
        ({ state, editor }) => {
          const trackState = trackPlugin.getState(state);
          // Store the track state in the editor's storage for external access
          if (editor && trackState) {
            editor.storage.trackchange.currentTrackState = trackState;
          }
          return true;
        },

      highlightCommit:
        (commit: Commit | null) =>
        ({ tr, dispatch }) => {
          if (dispatch) {
            if (commit) {
              tr.setMeta(highlightPluginKey, { add: commit });
            } else {
              tr.setMeta(highlightPluginKey, { clear: true });
            }
            dispatch(tr);
          }
          return true;
        },

      clearAllHighlights:
        () =>
        ({ tr, dispatch }) => {
          if (dispatch) {
            tr.setMeta(highlightPluginKey, { clear: true });
            dispatch(tr);
          }
          return true;
        },

      restoreLatestContent:
        () =>
        ({ tr, dispatch }) => {
          if (dispatch) {
            // Clear highlighting and force a clean state
            tr.setMeta(highlightPluginKey, { clear: true });
            dispatch(tr);
          }
          return true;
        },

      saveToBackend:
        () =>
        ({ state, editor }) => {
          try {
            const trackState = trackPlugin.getState(state);
            if (!trackState) {
              console.error("No track state found");
              return null;
            }

            // Use provided documentId or fall back to stored documentId
            const documentId = editor?.storage.trackchange.documentId;

            const documentPayload = {
              id: documentId,
              content: editor.getHTML({
                parseOptions: {
                  preserveWhitespace: "full",
                },
              }), // This is already HTML
              data: serializeTrackStateToAPI(trackState),
            };

            return documentPayload;
          } catch (error) {
            console.error("❌ Error preparing document for save:", error);
            return null;
          }
        },

      loadFromBackend:
        (documentData: any) =>
        ({ tr, dispatch, state, editor }) => {
          try {
            if (!dispatch || !editor || !documentData) return false;
            const htmlContent = documentData.content;
            const trackState = createTrackStateFromAPI(
              documentData,
              state.schema
            );
            // Convert HTML to JSON for proper ProseMirror structure
            const jsonContent = convertHTMLToJSON(htmlContent, state.schema);

            if (!jsonContent) {
              console.error("Failed to convert HTML to JSON");
              return false;
            }

            // Clear any existing highlights
            tr.setMeta(highlightPluginKey, { clear: true });

            // Replace content with the JSON-based document structure
            const newDoc = state.schema.nodeFromJSON(jsonContent);
            tr.replaceWith(0, tr.doc.content.size, newDoc.content);

            // Restore track state with full step history
            tr.setMeta("isRestoration", true);
            tr.setMeta("restoredTrackState", trackState);

            // Enable full checkout capability for loaded documents
            editor.storage.trackchange.isViewMode = false;
            editor.storage.trackchange.checkedOutCommitIndex = -1;

            dispatch(tr);

            return true;
          } catch (error) {
            console.error("❌ Error loading document from backend:", error);
            return false;
          }
        },

      saveDocument:
        (message?: string) =>
        ({ tr, dispatch, state }) => {
          const trackState = trackPlugin.getState(state);
          if (trackState?.uncommittedSteps.length > 0) {
            if (dispatch) {
              tr.setMeta(trackPluginKey, message || "Document saved");
              dispatch(tr);
            }
            return true;
          }
          return false;
        },

      checkoutCommit:
        (targetCommit: Commit, force: boolean = false) =>
        ({ tr, dispatch, state, editor }) => {
          if (!dispatch || !editor) return false;

          const trackState = trackPlugin.getState(state);
          if (!trackState) return false;

          const targetIndex = trackState.commits.indexOf(targetCommit);

          if (targetIndex === -1) {
            console.warn("Commit not found in history");
            return false;
          }

          if (force && trackState.uncommittedSteps.length > 0) {
            trackState.uncommittedSteps = [];
            trackState.uncommittedMaps = [];
            trackState.uncommittedOriginalSteps = [];
          } else if (trackState.uncommittedSteps.length > 0) {
            console.warn("Commit your changes first!");
            return false;
          }

          // Store the current state as latest before switching to view mode
          if (!editor.storage.trackchange.isViewMode) {
            editor.storage.trackchange.latestState = {
              content: state.doc,
              trackState: trackState,
            };
          }

          // Reconstruct the document at the target commit
          let reconstructedState = EditorState.create({
            schema: state.schema,
            plugins: state.plugins,
          });

          // Build blame map by replaying history up to targetIndex
          let blameMap: Span[] = [new Span(0, 0, null)];
          for (let i = 0; i <= targetIndex; i++) {
            const commit = trackState.commits[i];
            const workingTr = reconstructedState.tr;
            workingTr.setMeta("isCheckout", true);

            for (const step of commit.originalSteps) {
              workingTr.maybeStep(step);
            }

            // Update blame map for this commit's changes
            blameMap = updateBlameMap(blameMap, workingTr, i);
            reconstructedState = reconstructedState.apply(workingTr);
          }

          // Remove empty spans
          blameMap = blameMap.filter((span) => span.from < span.to);

          const targetTrackState = new TrackState(
            blameMap,
            trackState.commits.slice(0, targetIndex + 1),
            [],
            [],
            []
          );

          tr.setMeta("isCheckout", true);
          tr.replaceWith(
            0,
            tr.doc.content.size,
            reconstructedState.doc.content
          );
          tr.setMeta("restoredTrackState", targetTrackState);
          tr.setMeta("isRestoration", true);

          editor.storage.trackchange.isViewMode = true;
          editor.storage.trackchange.checkedOutCommitIndex = targetIndex;

          dispatch(tr);

          setTimeout(() => {
            const highlightTr = editor.state.tr.setMeta(highlightPluginKey, {
              add: targetCommit,
            });
            const newState = editor.state.apply(highlightTr);
            editor.view.updateState(newState);
          }, 0);

          return true;
        },

      returnToLatest:
        () =>
        ({ tr, dispatch, editor, state }) => {
          if (!dispatch || !editor) return false;

          const latestState = editor.storage.trackchange.latestState;

          if (!latestState) {
            console.warn("No latest state stored");
            return false;
          }

          // Clear highlighting first
          tr.setMeta(highlightPluginKey, { clear: true });

          // Replace the current document with the latest state
          tr.replaceWith(0, tr.doc.content.size, latestState.content);

          // CRITICAL: Mark this transaction as a special restoration transaction
          // and include the saved track state to restore
          tr.setMeta("isRestoration", true);
          tr.setMeta("restoredTrackState", latestState.trackState);

          // Reset view mode flags
          editor.storage.trackchange.isViewMode = false;
          editor.storage.trackchange.checkedOutCommitIndex = -1;
          editor.storage.trackchange.latestState = null;

          dispatch(tr);
          return true;
        },

      setDocumentId:
        (documentId: string) =>
        ({ editor }) => {
          if (editor) {
            editor.storage.trackchange.documentId = documentId;
          }
          return true;
        },
    };
  },

  addStorage() {
    return {
      hasUncommittedChanges: false,
      currentTrackState: null,
      latestState: null,
      isViewMode: false,
      checkedOutCommitIndex: -1,
      documentId: null, // Current document ID
    };
  },

  onTransaction({ editor }) {
    // Update storage to track if there are uncommitted changes
    const trackState = trackPlugin.getState(editor.state);
    this.storage.hasUncommittedChanges =
      trackState?.uncommittedSteps.length > 0;
  },
});

// Export types and classes for external use
export { Commit, TrackState, Span };

// Export utility functions
export * from "./utils";

export default TrackChangeExtension;
