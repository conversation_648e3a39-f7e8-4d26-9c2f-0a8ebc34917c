import React, { useState } from "react";
import { Editor } from "@/components/tiptap/editor";
import { Toolbar } from "@/components/tiptap/toolbar";
import { EditorCommentsProvider } from "@/contexts/EditorCommentsContext";
import { useEditorStore } from "@/store/use-editor-store";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Copy, Eye, EyeOff } from "lucide-react";
import { toast } from "sonner";

const DebugPanel = () => {
  const { editor } = useEditorStore();
  const [isVisible, setIsVisible] = useState(false);
  const [jsonData, setJsonData] = useState<string>("");

  const generateDebugData = () => {
    if (!editor) {
      toast.error("Editor not available");
      return;
    }

    try {
      // First commit any pending changes
      const generateDefaultCommitMessage = () => {
        const now = new Date();
        return `Auto-save ${now.toLocaleDateString()} ${now.toLocaleTimeString()}`;
      };

      editor.commands.saveDocument(generateDefaultCommitMessage());

      // Get the document payload that would be sent to backend
      const payload = editor.commands.saveToBackend();

      if (!payload) {
        toast.error("Failed to generate payload - no data available");
        return;
      }

      // Format the JSON for display
      const formattedJson = JSON.stringify(payload, null, 2);
      setJsonData(formattedJson);
      setIsVisible(true);
      toast.success("Debug data generated!");
    } catch (error) {
      console.error("Failed to generate debug data:", error);
      toast.error("Failed to generate debug data");
    }
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(jsonData);
      toast.success("JSON copied to clipboard!");
    } catch (error) {
      console.error("Failed to copy to clipboard:", error);
      toast.error("Failed to copy to clipboard");
    }
  };

  return (
    <Card className="mt-6">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Debug Panel - Backend Payload</span>
          <div className="flex gap-2">
            <Button onClick={generateDebugData} variant="outline" size="sm">
              Generate Debug Data
            </Button>
            <Button
              onClick={() => setIsVisible(!isVisible)}
              variant="outline"
              size="sm"
            >
              {isVisible ? (
                <EyeOff className="h-4 w-4" />
              ) : (
                <Eye className="h-4 w-4" />
              )}
              {isVisible ? "Hide" : "Show"}
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      {isVisible && (
        <CardContent>
          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              This shows the exact JSON payload that would be sent to the
              backend when saving the document. Click "Generate Debug Data" to
              capture the current state.
            </p>
            {jsonData && (
              <div className="relative">
                <Button
                  onClick={copyToClipboard}
                  className="absolute top-2 right-2 z-10"
                  size="sm"
                  variant="outline"
                >
                  <Copy className="h-4 w-4" />
                  Copy
                </Button>
                <Textarea
                  value={jsonData}
                  readOnly
                  className="font-mono text-xs min-h-[300px] max-h-[500px] overflow-auto bg-gray-50"
                  placeholder="Click 'Generate Debug Data' to see the JSON payload..."
                />
              </div>
            )}
          </div>
        </CardContent>
      )}
    </Card>
  );
};

const TestCommitsPage = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto py-8">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-2xl font-bold mb-6">Test Commits Dropdown</h1>

          {/* Debug Panel - Moved to top for better visibility */}
          <DebugPanel />

          <div className="mb-4 mt-6"></div>
          <EditorCommentsProvider>
            <div className="border rounded-lg overflow-hidden">
              <div className="bg-gray-100 p-2">
                <Toolbar />
              </div>
              <div className="min-h-[400px]">
                <Editor documentId="test-document" documentMode="edit" />
              </div>
            </div>
          </EditorCommentsProvider>

          <div className="mt-4 p-4 bg-blue-50 rounded-lg">
            <h3 className="font-semibold text-blue-900 mb-2">
              Expected Behavior:
            </h3>
            <ul className="list-disc list-inside space-y-1 text-sm text-blue-800">
              <li>
                The commits dropdown should show "No commits yet" initially
              </li>
              <li>
                After saving changes, commits should appear in the dropdown
              </li>
              <li>
                Clicking a commit should highlight the changes with a yellow
                background
              </li>
              <li>
                The selected commit should show "Highlighted" status in the
                dropdown
              </li>
              <li>
                Changes should be visually highlighted in the editor content
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestCommitsPage;
