import puppeteer from "puppeteer";

async function testTrackStateExtraction() {
  let browser = null;
  let page = null;

  try {
    console.log("🚀 Starting Puppeteer test for trackState extraction...");

    // Launch browser
    browser = await puppeteer.launch({
      headless: false, // Set to true for headless mode
      devtools: false,
    });

    page = await browser.newPage();
    await page.setViewport({ width: 1920, height: 1024 });

    // Navigate to test-commits page
    console.log("📍 Navigating to test-commits page...");
    await page.goto("http://localhost:3000/test-commits", {
      waitUntil: "networkidle2",
      timeout: 30000,
    });

    // Wait for editor to load
    console.log("⏳ Waiting for TipTap editor to load...");
    await page.waitForSelector(".ProseMirror", { timeout: 15000 });

    // Add sample content
    console.log("✍️ Adding sample content to editor...");
    await page.click(".ProseMirror");
    await page.type(
      ".ProseMirror",
      "This is the initial content for testing change tracking."
    );

    // First save
    console.log("💾 Triggering first save...");
    await page.keyboard.down("Control");
    await page.keyboard.press("s");
    await page.keyboard.up("Control");

    // Add more content
    console.log("✍️ Adding more content...");
    await page.keyboard.press("Enter");
    await page.type(
      ".ProseMirror",
      "\nThis is additional content added after the first save."
    );

    // Second save
    console.log("💾 Triggering second save...");
    await page.keyboard.down("Control");
    await page.keyboard.press("s");
    await page.keyboard.up("Control");

    // Make edits
    console.log("✏️ Making edits to existing content...");
    await page.keyboard.down("Control");
    await page.keyboard.press("a");
    await page.keyboard.up("Control");
    await page.keyboard.press("ArrowRight");
    await page.type(".ProseMirror", " EDITED");

    // Third save
    console.log("💾 Triggering third save...");
    await page.keyboard.down("Control");
    await page.keyboard.press("s");
    await page.keyboard.up("Control");

    // Find and click debug button
    console.log("🔍 Looking for debug panel...");

    // Try multiple selector strategies for the debug button
    let debugButton = null;
    try {
      // First try the text-based selector
      await page.waitForSelector('button:has-text("Generate Debug Data")', {
        timeout: 5000,
      });
      debugButton = await page.$('button:has-text("Generate Debug Data")');
    } catch (error) {
      console.log("Text selector failed, trying alternative selectors...");

      // Try finding button by text content
      debugButton = await page.evaluateHandle(() => {
        const buttons = Array.from(document.querySelectorAll("button"));
        return buttons.find(
          (button) =>
            button.textContent &&
            button.textContent.includes("Generate Debug Data")
        );
      });

      if (!debugButton || debugButton.asElement() === null) {
        throw new Error("Could not find Generate Debug Data button");
      }
    }

    console.log("🖱️ Scrolling to and clicking Generate Debug Data button...");

    // Scroll the button into view
    await page.evaluate((button) => {
      if (button) {
        button.scrollIntoView({ behavior: "smooth", block: "center" });
      }
    }, debugButton);

    // Click the button
    await debugButton.click();

    // Extract JSON data
    console.log("📊 Extracting JSON payload...");
    await page.waitForSelector("textarea[readonly]", { timeout: 10000 });

    const jsonPayload = await page.evaluate(() => {
      const textarea = document.querySelector("textarea[readonly]");
      if (!textarea || !textarea.value) {
        throw new Error("No JSON data found in debug panel");
      }

      try {
        return JSON.parse(textarea.value);
      } catch (error) {
        throw new Error("Failed to parse JSON data: " + error.message);
      }
    });

    // Extract and validate trackState
    console.log("🎯 Extracting trackState data...");
    const trackStateData = jsonPayload.data;

    if (!trackStateData) {
      throw new Error("No trackState data found in JSON payload");
    }

    const { blameMap, commits } = trackStateData;

    if (!blameMap || !commits) {
      throw new Error(
        "Invalid trackState structure - missing blameMap or commits"
      );
    }

    console.log("✅ Success! TrackState extracted successfully:");
    console.log(`   📝 Document ID: ${jsonPayload.id}`);
    console.log(`   📊 Commits: ${commits.length}`);
    console.log(`   🗺️ BlameMap entries: ${blameMap.length}`);
    console.log(
      `   📄 Content length: ${jsonPayload.content ? jsonPayload.content.length : 0} characters`
    );

    // Log first few commits for inspection
    console.log("\n📋 Commit details:");
    commits.forEach((commit, index) => {
      console.log(`   ${index + 1}. ${commit.message} (${commit.timestamp})`);
    });

    return {
      success: true,
      trackState: trackStateData,
      summary: {
        commitsCount: commits.length,
        blameMapEntries: blameMap.length,
        documentId: jsonPayload.id,
        contentLength: jsonPayload.content ? jsonPayload.content.length : 0,
      },
    };
  } catch (error) {
    console.error("❌ Error in test:", error.message);

    // Capture screenshot for debugging
    if (page) {
      try {
        await page.screenshot({ path: "error-screenshot.png" });
        console.log("📸 Screenshot saved as error-screenshot.png");
      } catch (screenshotError) {
        console.error("Failed to capture screenshot:", screenshotError);
      }
    }

    return {
      success: false,
      error: error.message,
    };
  } finally {
    if (page) await page.close();
    if (browser) await browser.close();
  }
}

// Run the test
testTrackStateExtraction()
  .then((result) => {
    if (result.success) {
      console.log("\n🎉 Test completed successfully!");
      console.log(
        "TrackState data structure:",
        JSON.stringify(result.trackState, null, 2)
      );
    } else {
      console.log("\n💥 Test failed:", result.error);
    }
  })
  .catch((error) => {
    console.error("💥 Unexpected error:", error);
  });
